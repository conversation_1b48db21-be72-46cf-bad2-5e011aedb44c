@import "tailwindcss";

:root {
  --background: #FFF3E0;
  --foreground: #401A06;
  --button: #008080E6;
  --card-bg: #fffefb;
  --card-fg: #401A06;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #1e180e;
    --foreground: #FAFAF9;
    --button: #00B3B3;
    --card-bg: #FAFAF9;
    --card-fg: #1e180e;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
}

.dark {
  --background: #1e180e;
  --foreground: #FAFAF9;
  --button: #00B3B3;
}

.light {
  --background: #FFF3E0;
  --foreground: #401A06;
  --button: #008080E6;
}

/* Krishna part */
.title {
  font-family: 'Philosopher', serif;
  font-weight: 700;
}


.title_jp {
  font-family: 'Kaisei Tokumin', serif;
  font-weight: 700;
}

/* Add hyphenation support for Sanskrit terms */
.sanskrit-term {
  hyphens: auto;
  overflow-wrap: break-word;
  word-break: break-word;
}
