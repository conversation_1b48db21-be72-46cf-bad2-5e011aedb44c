const fs = require('fs');
const inputPath = './src/services/terminology.ts';
const outputPath = './src/services/terminology.sorted.ts';

const content = fs.readFileSync(inputPath, 'utf8');

// Extract the object content (removing the export line and possible trailing semicolon)
const match = content.match(/export const yoga_sutra_terms\s*=\s*{([\s\S]*)}\s*$/);
if (!match) {
  throw new Error('Could not find yoga_sutra_terms object');
}
const objText = `{${match[1]}}`;

const evalObj = eval('(' + objText + ')');

const sorted = Object.fromEntries(
  Object.entries(evalObj).sort(([a], [b]) => a.localeCompare(b))
);

const sortedStr = JSON.stringify(sorted, null, 4)
  .replace(/"(\w+)":/g, '$1:') // remove quotes from keys
  .replace(/},\n\s*"/g, '},\n\n    "'); // spacing for clarity

fs.writeFileSync(
  outputPath,
  '// This file is generated by sorting terminology.ts alphabetically by key.\n\n' +
  'export const yoga_sutra_terms = ' + sortedStr + ';\n'
);

console.log('Sorted terminology file written to terminology.sorted.ts');